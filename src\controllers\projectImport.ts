import { Prisma } from '@prisma/client';
import { FastifyReply, FastifyRequest } from 'fastify';
import { DatabaseService } from '../services/database.js';
import {
    ContractSigningStatus,
    ContractType,
    DEFAULT_EXCEL_PARSE_CONFIG,
    DocumentType,
    ExcelImportResult,
    ExcelProjectRow,
    ProjectStatus,
    RevenueStatus,
    RevenueType
} from '../types/project.js';

export class ProjectImportController {
  private db: DatabaseService;

  constructor() {
    this.db = new DatabaseService();
  }

  /**
   * 解析Excel/CSV文件
   */
  private parseExcelFile(buffer: Buffer, mimeType: string): ExcelProjectRow[] {
    try {
      if (mimeType === 'text/csv') {
        return this.parseCSVFile(buffer);
      } else {
        // 暂时只支持CSV，Excel支持需要安装xlsx包
        throw new Error('暂时只支持CSV文件格式，请将Excel文件另存为CSV格式后上传');
      }
    } catch (error) {
      console.error('解析文件失败:', error);
      throw new Error(`解析文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析CSV文件
   */
  private parseCSVFile(buffer: Buffer): ExcelProjectRow[] {
    const csvData = buffer.toString('utf8');
    const lines = csvData.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length < 2) {
      throw new Error('CSV文件中没有数据行');
    }

    // 第一行作为表头
    const headers = this.parseCSVLine(lines[0] || '');
    const dataRows = lines.slice(1).map(line => this.parseCSVLine(line));

    // 创建字段映射
    const fieldMapping = this.createFieldMapping(headers);

    // 转换数据行
    const result: ExcelProjectRow[] = [];
    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      if (!row || this.isEmptyRow(row)) continue;

      const mappedRow = this.mapRowToExcelProjectRow(row, fieldMapping, headers);
      if (mappedRow) {
        result.push(mappedRow);
      }
    }

    return result;
  }

  /**
   * 解析CSV行（处理逗号分隔和引号包围的值）
   */
  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  }

  /**
   * 创建字段映射
   */
  private createFieldMapping(headers: string[]): Record<string, number> {
    const mapping: Record<string, number> = {};

    // 字段映射表（支持中英文字段名）
    const fieldMap: Record<string, string[]> = {
      orderTime: ['orderTime', '下单时间', '订单时间'],
      brandName: ['brandName', '品牌名称', '品牌'],
      executeProject: ['executeProject', '执行项目', '项目类型'],
      projectName: ['projectName', '项目名称', '项目'],
      contractSigningStatus: ['contractSigningStatus', '合同签署状况', '合同状态'],
      contractType: ['contractType', '合同类型'],
      planningBudget: ['planningBudget', '规划预算', '预算'],
      expectedPaymentMonth: ['expectedPaymentMonth', '预计回款月份', '回款月份'],
      paymentTermDays: ['paymentTermDays', '账期'],
      period: ['period', '周期', '执行周期'],
      amountReceived: ['amountReceived', '已汇款', '已收款'],
      unpaidAmount: ['unpaidAmount', '未付款'],
      reimbursementStatus: ['reimbursementStatus', '回款状态'],
      cost: ['cost', '成本'],
      estimatedInfluencerRebate: ['estimatedInfluencerRebate', '预估达人返点', '返点'],
      intermediary: ['intermediary', '中介', 'Intermediary']
    };

    // 遍历表头，建立映射关系
    headers.forEach((header, index) => {
      const trimmedHeader = header?.toString().trim();
      if (!trimmedHeader) return;

      // 查找匹配的字段
      for (const [fieldName, aliases] of Object.entries(fieldMap)) {
        if (aliases.some(alias => alias === trimmedHeader)) {
          mapping[fieldName] = index;
          break;
        }
      }
    });

    return mapping;
  }

  /**
   * 检查是否为空行
   */
  private isEmptyRow(row: any[]): boolean {
    return !row || row.every(cell => !cell || cell.toString().trim() === '');
  }

  /**
   * 将数据行映射为ExcelProjectRow
   */
  private mapRowToExcelProjectRow(row: any[], mapping: Record<string, number>, _headers: string[]): ExcelProjectRow | null {
    try {
      const getValue = (fieldName: string): string => {
        const index = mapping[fieldName];
        return index !== undefined ? (row[index]?.toString().trim() || '') : '';
      };

      // 检查必填字段
      const projectName = getValue('projectName');
      const brandName = getValue('brandName');

      if (!projectName || !brandName) {
        console.warn(`跳过无效行: 项目名称="${projectName}", 品牌名称="${brandName}"`);
        return null;
      }

      return {
        orderTime: getValue('orderTime'),
        brandName: getValue('brandName'),
        executeProject: getValue('executeProject'),
        projectName: getValue('projectName'),
        contractSigningStatus: getValue('contractSigningStatus'),
        contractType: getValue('contractType'),
        planningBudget: getValue('planningBudget'),
        expectedPaymentMonth: getValue('expectedPaymentMonth'),
        paymentTermDays: getValue('paymentTermDays'),
        period: getValue('period'),
        amountReceived: getValue('amountReceived'),
        unpaidAmount: getValue('unpaidAmount'),
        reimbursementStatus: getValue('reimbursementStatus'),
        cost: getValue('cost'),
        estimatedInfluencerRebate: getValue('estimatedInfluencerRebate'),
        intermediary: getValue('intermediary')
      };
    } catch (error) {
      console.error('映射数据行失败:', error);
      return null;
    }
  }

  /**
   * 批量导入项目
   */
  async importProjects(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userId = (request as any).user?.userid;

      // 获取查询参数作为选项
      const query = request.query as any;
      const options = {
        createMissingBrands: query.createMissingBrands === 'true' || query.createMissingBrands === true,
        defaultExecutorPM: query.defaultExecutorPM || userId,
        defaultContentMediaIds: query.defaultContentMediaIds ? query.defaultContentMediaIds.split(',') : [],
        dryRun: query.dryRun === 'true' || query.dryRun === true
      };

      // 处理文件上传
      const uploadedFile = await request.file();
      if (!uploadedFile) {
        return reply.status(400).send({
          success: false,
          message: '请上传Excel文件'
        });
      }

      // 验证文件类型（暂时只支持CSV）
      const allowedMimeTypes = [
        'text/csv', // .csv
        'application/csv',
        'text/plain' // 有些系统可能将CSV识别为text/plain
      ];

      if (!allowedMimeTypes.includes(uploadedFile.mimetype)) {
        return reply.status(400).send({
          success: false,
          message: '暂时只支持CSV文件格式，请将Excel文件另存为CSV格式后上传'
        });
      }

      // 读取文件内容
      const buffer = await uploadedFile.toBuffer();

      // 解析Excel文件
      const data = this.parseExcelFile(buffer, uploadedFile.mimetype);

      if (!data || data.length === 0) {
        return reply.status(400).send({
          success: false,
          message: 'Excel文件中没有有效数据'
        });
      }

      const result: ExcelImportResult = {
        success: true,
        totalRows: data.length,
        successCount: 0,
        failureCount: 0,
        errors: [],
        createdProjects: [],
        warnings: []
      };

      // 如果是试运行，只验证不创建
      if (options.dryRun) {
        for (let i = 0; i < data.length; i++) {
          const row = data[i];
          if (!row) continue;
          const validation = await this.validateRow(row, i + 1, options);
          if (!validation.valid) {
            result.errors.push(...validation.errors);
            result.failureCount++;
          } else {
            result.successCount++;
          }
          if (validation.warnings.length > 0) {
            result.warnings.push(...validation.warnings);
          }
        }
        
        return reply.send({
          success: true,
          data: result,
          message: `验证完成：${result.successCount}行有效，${result.failureCount}行有错误`
        });
      }

      // 实际导入
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 1;
        if (row) {
          console.log(`导入第${rowNumber}行: ${row.projectName}`);

          try {
            // 验证数据
            const validation = await this.validateRow(row, rowNumber, options);
            if (!validation.valid) {
              result.errors.push(...validation.errors);
              result.failureCount++;
              continue;
            }

            if (validation.warnings.length > 0) {
              result.warnings.push(...validation.warnings);
            }

            // 转换数据
            const projectData = await this.convertRowToProject(row, options, userId);

            // 创建项目
            const project = await this.db.client.project.create({
              data: projectData
            });

            // 创建收入记录
            let revenueId: string | undefined;
            const revenues = this.createRevenuesFromRow(row, project.id, userId, projectData.createdAt);

            if (revenues.length > 0) {
              const createdRevenues = [];
              for (const revenueData of revenues) {
                const revenue = await this.db.client.projectRevenue.create({
                  data: revenueData,
                  include: {
                    project: {
                      include: {
                        brand: true
                      }
                    }
                  }
                });
                createdRevenues.push(revenue.id);
              }
              revenueId = createdRevenues[0]; // 返回第一个收入记录ID
            }

            result.createdProjects.push({
              row: rowNumber,
              projectId: project.id,
              projectName: project.projectName,
              revenueId
            });
            result.successCount++;

          } catch (error) {
            console.error(`导入第${rowNumber}行时出错:`, error);
            result.errors.push({
              row: rowNumber,
              message: error instanceof Error ? error.message : '未知错误',
              data: row
            });
            result.failureCount++;
          }
          
        }
      }

      result.success = result.failureCount === 0;

      return reply.send({
        success: true,
        data: result,
        message: `导入完成：成功${result.successCount}个，失败${result.failureCount}个`
      });

    } catch (error) {
      console.error('批量导入项目失败:', error);
      return reply.status(500).send({
        success: false,
        message: '批量导入失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 验证单行数据
   */
  private async validateRow(row: ExcelProjectRow, rowNumber: number, options: any) {
    const errors: any[] = [];
    const warnings: any[] = [];

    // 必填字段验证
    if (!row.projectName?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'projectName',
        message: '项目名称不能为空'
      });
    }

    if (!row.brandName?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'brandName',
        message: '品牌名称不能为空'
      });
    }

    if (!row.planningBudget?.trim()) {
      errors.push({
        row: rowNumber,
        field: 'planningBudget',
        message: '规划预算不能为空'
      });
    }

    // 验证品牌是否存在
    if (row.brandName?.trim()) {
      const brand = await this.db.client.brand.findFirst({
        where: { name: row.brandName.trim() }
      });
      
      if (!brand && !options.createMissingBrands) {
        errors.push({
          row: rowNumber,
          field: 'brandName',
          message: `品牌"${row.brandName}"不存在，请先创建品牌或启用自动创建选项`
        });
      } else if (!brand && options.createMissingBrands) {
        warnings.push({
          row: rowNumber,
          message: `将自动创建品牌"${row.brandName}"`
        });
      }
    }

    // 验证金额格式
    try {
      this.parseAmount(row.planningBudget);
    } catch (error) {
      errors.push({
        row: rowNumber,
        field: 'planningBudget',
        message: `规划预算格式错误: ${row.planningBudget}`
      });
    }

    if (row.cost?.trim()) {
      try {
        this.parseAmount(row.cost);
      } catch (error) {
        errors.push({
          row: rowNumber,
          field: 'cost',
          message: `成本格式错误: ${row.cost}`
        });
      }
    }

    // 验证枚举值
    if (row.contractSigningStatus && !DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap[row.contractSigningStatus]) {
      errors.push({
        row: rowNumber,
        field: 'contractSigningStatus',
        message: `不支持的合同签署状态: ${row.contractSigningStatus}`
      });
    }

    if (row.contractType && !DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap[row.contractType]) {
      errors.push({
        row: rowNumber,
        field: 'contractType',
        message: `不支持的合同类型: ${row.contractType}`
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 解析金额字符串（支持"万"单位）
   */
  private parseAmount(amountStr: string): number {
    if (!amountStr?.trim()) return 0;
    
    const cleanStr = amountStr.trim().replace(/[,，]/g, '');
    
    // 处理"万"单位
    if (cleanStr.includes('万')) {
      const numStr = cleanStr.replace('万', '');
      const num = parseFloat(numStr);
      if (isNaN(num)) {
        throw new Error(`无法解析金额: ${amountStr}`);
      }
      return num * 10000; // 转换为元
    }
    
    const num = parseFloat(cleanStr);
    if (isNaN(num)) {
      throw new Error(`无法解析金额: ${amountStr}`);
    }
    
    return num;
  }

  /**
   * 解析账期天数
   */
  private parsePaymentTermDays(termStr: string): number | undefined {
    if (!termStr?.trim()) return undefined;
    
    const match = termStr.match(/T\+(\d+)/i);
    if (match && match[1]) {
      return parseInt(match[1]);
    }
    
    return undefined;
  }

  /**
   * 解析执行周期
   */
  private parsePeriod(periodStr: string, orderTime: string) {
    // 从orderTime解析基准年份
    let baseYear = new Date().getFullYear();
    if (orderTime) {
      const yearMatch = orderTime.match(/(\d{4})/);
      if (yearMatch && yearMatch[1]) {
        baseYear = parseInt(yearMatch[1]);
      }
    }

    // 默认为整年，使用中午时间避免时区问题
    let startDate = new Date(baseYear, 0, 1, 12, 0, 0, 0);
    let endDate = new Date(baseYear, 11, 31, 12, 0, 0, 0);

    // 解析period字段的月份范围
    if (periodStr) {
      // 匹配 "1-2月" 格式
      const rangeMatch = periodStr.match(/(\d{1,2})-(\d{1,2})月/);
      if (rangeMatch && rangeMatch[1] && rangeMatch[2]) {
        const startMonth = parseInt(rangeMatch[1]) - 1; // 月份从0开始
        const endMonth = parseInt(rangeMatch[2]) - 1;

        startDate = new Date(baseYear, startMonth, 1, 12, 0, 0, 0);
        endDate = new Date(baseYear, endMonth + 1, 0, 12, 0, 0, 0); // 月末最后一天
      } else {
        // 匹配单个月份 "1月" 格式
        const singleMonthMatch = periodStr.match(/(\d{1,2})月/);
        if (singleMonthMatch && singleMonthMatch[1]) {
          const month = parseInt(singleMonthMatch[1]) - 1;
          startDate = new Date(baseYear, month, 1, 12, 0, 0, 0);
          endDate = new Date(baseYear, month + 1, 0, 12, 0, 0, 0);
        }
      }
    }
    console.log(`解析周期: ${periodStr} -> ${startDate.toLocaleDateString()} 到 ${endDate.toLocaleDateString()}`);

    return { startDate, endDate };
  }

  /**
   * 解析orderTime作为创建时间
   */
  private parseOrderTime(orderTime: string): Date {
    if (!orderTime) return new Date();

    // 匹配 "2025年1月" 格式
    const match = orderTime.match(/(\d{4})年(\d{1,2})月/);
    if (match && match[1] && match[2]) {
      const year = parseInt(match[1]);
      const month = parseInt(match[2]) - 1; // 月份从0开始
      // 使用本地时区的中午时间，避免时区转换问题
      return new Date(year, month, 1, 12, 0, 0, 0);
    }

    // 匹配纯数字格式 "20250101"
    const dateMatch = orderTime.match(/^(\d{4})(\d{2})(\d{2})$/);
    if (dateMatch && dateMatch[1] && dateMatch[2] && dateMatch[3]) {
      const year = parseInt(dateMatch[1]);
      const month = parseInt(dateMatch[2]) - 1; // 月份从0开始
      const day = parseInt(dateMatch[3]);
      // 使用本地时区的中午时间，避免时区转换问题
      return new Date(year, month, day, 12, 0, 0, 0);
    }

    // 如果只有年份
    const yearMatch = orderTime.match(/(\d{4})/);
    if (yearMatch && yearMatch[1]) {
      const year = parseInt(yearMatch[1]);
      return new Date(year, 0, 1, 12, 0, 0, 0);
    }

    return new Date();
  }

  /**
   * 转换Excel行数据为项目创建请求
   */
  private async convertRowToProject(row: ExcelProjectRow, options: any, userId: string): Promise<any> {
    // 获取或创建品牌
    let brand = await this.db.client.brand.findFirst({
      where: { name: row.brandName.trim() }
    });

    if (!brand && options.createMissingBrands) {
      brand = await this.db.client.brand.create({
        data: {
          name: row.brandName.trim(),
          status: 'ACTIVE',
          createdBy: userId
        }
      });
    }

    if (!brand) {
      throw new Error(`品牌"${row.brandName}"不存在`);
    }

    // 解析金额
    const planningBudget = this.parseAmount(row.planningBudget);
    const totalCost = row.cost ? this.parseAmount(row.cost) : 0;
    const estimatedInfluencerRebate = row.estimatedInfluencerRebate ? this.parseAmount(row.estimatedInfluencerRebate) : 0;

    // 成本均分到三个类别
    const costPerCategory = totalCost / 3;
    const influencerCost = costPerCategory;
    const adCost = costPerCategory;
    const otherCost = costPerCategory;

    // 解析周期
    const period = this.parsePeriod(row.period, row.orderTime);

    // 解析账期
    const paymentTermDays = this.parsePaymentTermDays(row.paymentTermDays);

    // 解析预计回款月份
    let expectedPaymentMonth: string | undefined;
    if (row.expectedPaymentMonth) {
      const match = row.expectedPaymentMonth.match(/(\d{4})年(\d{1,2})月/);
      if (match && match[1] && match[2]) {
        const year = match[1];
        const month = match[2].padStart(2, '0');
        expectedPaymentMonth = `${year}-${month}`;
      }
    }

    // 解析创建时间
    const createdAt = this.parseOrderTime(row.orderTime);

    // 映射枚举值
    const documentType = DocumentType.PROJECT_INITIATION;
    const contractType = DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap[row.contractType] || ContractType.SINGLE;
    const contractSigningStatus = DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap[row.contractSigningStatus] || ContractSigningStatus.PENDING;

    // 计算利润
    const profit = planningBudget - totalCost + estimatedInfluencerRebate;
    const grossMargin = planningBudget > 0 ? (profit / planningBudget) * 100 : 0;
    let projectStatus: ProjectStatus = ProjectStatus.DRAFT;
    if (period.startDate < new Date()) {
      console.log(`项目已开始: ${row.projectName}`);
      projectStatus = ProjectStatus.ACTIVE;
    }
    if (period.endDate < new Date()) {
      console.log(`项目已结束: ${row.projectName}`);
      projectStatus = ProjectStatus.COMPLETED;
    }

    const projectData: Prisma.ProjectCreateInput = {
      documentType: documentType.toUpperCase() as any,
      brand: {
        connect: { id: brand.id }
      },
      projectName: row.projectName.trim(),
      startDate: period.startDate,
      endDate: period.endDate,
      planningBudget,
      influencerBudget: planningBudget,
      adBudget: 0,
      otherBudget: 0,
      expectedPaymentMonth: expectedPaymentMonth,
      paymentTermDays,
      influencerCost,
      adCost,
      otherCost,
      estimatedInfluencerRebate,
      executorPM: '6157664557692733',
      contentMediaIds: options.defaultContentMediaIds || [],
      contractType: contractType.toUpperCase() as any,
      contractSigningStatus: contractSigningStatus.toUpperCase() as any,
      settlementRules: '根据合同约定执行',
      kpi: '根据项目需求制定',
      status: projectStatus.toUpperCase() as any,
      createdAt,
      createdBy: userId,
      updatedBy: userId
    };

    // const data: Prisma.ProjectCreateInput = {
    //       documentType: projectData.documentType.toUpperCase() as any,
    //       projectName: projectData.projectName,
    //       startDate: 
    //       endDate: new Date(data.period.endDate),
    //       planningBudget: new Decimal(data.budget.planningBudget),
    //       influencerBudget: new Decimal(data.budget.influencerBudget),
    //       adBudget: new Decimal(data.budget.adBudget),
    //       otherBudget: new Decimal(data.budget.otherBudget),
    //       influencerCost: new Decimal(data.cost.influencerCost),
    //       adCost: new Decimal(data.cost.adCost),
    //       otherCost: new Decimal(data.cost.otherCost),
    //       estimatedInfluencerRebate: new Decimal(data.cost.estimatedInfluencerRebate),
    //       executorPM: data.executorPM,
    //       contentMediaIds: data.contentMediaIds,
    //       contractType: data.contractType.toUpperCase() as any,
    //       contractSigningStatus: (data.contractSigningStatus || 'PENDING').toUpperCase() as any,
    //       settlementRules: data.settlementRules,
    //       kpi: data.kpi,
    //       expectedPaymentMonth: data.expectedPaymentMonth,
    //       paymentTermDays: data.paymentTermDays,
    //       createdBy,
    //       updatedBy: createdBy,
    //       brand: {
    //         connect: { id: data.brandId }
    //       }
    //     };

    return projectData;
  }

  /**
   * 从Excel行数据创建收入记录（支持多笔收入）
   */
  private createRevenuesFromRow(row: ExcelProjectRow, projectId: string, userId: string, createdAt: Date): any[] {
    const planningBudget = this.parseAmount(row.planningBudget);
    const amountReceived = this.parseAmount(row.amountReceived);

    const revenues: any[] = [];

    // 如果没有已汇款金额，不创建收入记录
    if (amountReceived === 0) {
      return revenues;
    }

    // 如果已汇款金额等于规划预算，创建一笔已收款的收入
    if (amountReceived === planningBudget) {
      revenues.push({
        title: `${row.projectName} - 项目收入`,
        revenueType: RevenueType.PROJECT_INCOME.toUpperCase() as any,
        status: RevenueStatus.RECEIVED.toUpperCase() as any,
        plannedAmount: amountReceived,
        actualAmount: amountReceived,
        plannedDate: createdAt,
        confirmedDate: createdAt,
        receivedDate: createdAt,
        milestone: '项目收入',
        notes: `从Excel导入的收入记录（已收款）`,
        projectId,
        createdAt,
        createdBy: userId,
        updatedBy: userId
      });
    }
    // 如果已汇款金额小于规划预算，创建两笔收入：一笔已收款，一笔收款中
    else if (amountReceived < planningBudget) {
      const remainingAmount = planningBudget - amountReceived;

      // 第一笔：已收款
      revenues.push({
        title: `${row.projectName} - 项目收入（已收款）`,
        revenueType: RevenueType.PROJECT_INCOME.toUpperCase() as any,
        status: RevenueStatus.RECEIVED.toUpperCase() as any,
        plannedAmount: amountReceived,
        actualAmount: amountReceived,
        plannedDate: createdAt,
        confirmedDate: createdAt,
        receivedDate: createdAt,
        milestone: '项目收入 - 第一期',
        notes: `从Excel导入的收入记录（已收款部分）`,
        projectId,
        createdAt,
        createdBy: userId,
        updatedBy: userId
      });

      // 第二笔：收款中
      revenues.push({
        title: `${row.projectName} - 项目收入（收款中）`,
        revenueType: RevenueType.PROJECT_INCOME.toUpperCase() as any,
        status: RevenueStatus.RECEIVING.toUpperCase() as any,
        plannedAmount: remainingAmount,
        plannedDate: createdAt,
        milestone: '项目收入 - 第二期',
        notes: `从Excel导入的收入记录（待收款部分）`,
        projectId,
        createdAt,
        createdBy: userId,
        updatedBy: userId
      });
    }

    return revenues;
  }

  /**
   * 获取导入模板
   */
  async getImportTemplate(_request: FastifyRequest, reply: FastifyReply) {
    try {
      const template = {
        headers: [
          'orderTime',
          'brandName',
          'executeProject',
          'projectName',
          'contractSigningStatus',
          'contractType',
          'planningBudget',
          'expectedPaymentMonth',
          'paymentTermDays',
          'period',
          'amountReceived',
          'unpaidAmount',
          'reimbursementStatus',
          'cost',
          'estimatedInfluencerRebate',
          'intermediary'
        ],
        headerLabels: {
          orderTime: '下单时间',
          brandName: '品牌名称',
          executeProject: '执行项目',
          projectName: '项目名称',
          contractSigningStatus: '合同签署状况',
          contractType: '合同类型',
          planningBudget: '规划预算',
          expectedPaymentMonth: '预计回款月份',
          paymentTermDays: '账期',
          period: '周期',
          amountReceived: '已汇款',
          unpaidAmount: '未付款',
          reimbursementStatus: '回款状态',
          cost: '成本',
          estimatedInfluencerRebate: '预估达人返点',
          intermediary: '中介'
        },
        example: {
          orderTime: '2025年1月',
          brandName: '华帝',
          executeProject: '总项目',
          projectName: '华帝2501站外推广',
          contractSigningStatus: '签订中',
          contractType: '年框-合同',
          planningBudget: '591.3万',
          expectedPaymentMonth: '2025年5月',
          paymentTermDays: 'T+90',
          period: '1-2月',
          amountReceived: '59.9万',
          unpaidAmount: '0.0万',
          reimbursementStatus: '回款中',
          cost: '52.9万',
          estimatedInfluencerRebate: '5.7万',
          intermediary: ''
        },
        enums: {
          contractSigningStatus: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.contractSigningStatusMap),
          contractType: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.contractTypeMap),
          executeProject: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.documentTypeMap),
          reimbursementStatus: Object.keys(DEFAULT_EXCEL_PARSE_CONFIG.revenueStatusMap)
        }
      };

      return reply.send({
        success: true,
        data: template,
        message: '获取导入模板成功'
      });

    } catch (error) {
      console.error('获取导入模板失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取导入模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
}
